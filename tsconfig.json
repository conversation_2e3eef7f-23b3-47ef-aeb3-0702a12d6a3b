{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"shared/*": ["packages/shared/*"], "webapp/*": ["packages/webapp/*"], "@esenpai/supabase/*": ["supabase/*"]}}, "exclude": ["node_modules", ".next", "dist"]}