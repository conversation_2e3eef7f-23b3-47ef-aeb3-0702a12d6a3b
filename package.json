{"name": "esenpai", "version": "1.0.0", "description": "", "type": "module", "scripts": {"test": "vitest run", "test:supabase": "vitest run supabase/__tests__ --exclude supabase/__tests__/rate-limit.test.ts", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "check:supabase": "tsc -p ./supabase/__tests__/tsconfig.json --noEmit & eslint ./supabase/__tests__", "lint": "eslint .", "lint:page-props": "node ./scripts/check-page-props.js", "lint:shared-imports": "node ./scripts/check-shared-imports.js", "next:upgrade": "pnpm i next@latest react@latest react-dom@latest eslint-config-next@latest", "supabase:upgrade": "pnpm i @supabase/supabase-js@latest @supabase/ssr@latest", "supabase:start": "supabase start", "supabase:stop": "supabase stop --no-backup", "supabase:types": "supabase gen types typescript --local > ./supabase/lib/database.ts", "supabase:reset-remote": ".\\supabase\\scripts\\reset-remote.bat", "supabase:reset-db-local": "tsx supabase/scripts/reset-db-local.ts", "supabase:backup-db-local": "supabase db dump --local --file supabase/.seeds/data.sql --data-only", "supabase:reset-db-local-and-types": "pnpm supabase:reset-db-local && pnpm supabase:types", "sql:run": "tsx supabase/scripts/run-sql-file.ts"}, "private": true, "workspaces": ["packages/*", "supabase"], "dependencies": {"@hcaptcha/react-hcaptcha": "^1.12.0", "@react-email/render": "^1.1.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-form": "^1.12.0", "@vercel/speed-insights": "^1.2.0", "dotenv-mono": "^1.3.14", "form-data": "^4.0.2", "lucide-react": "^0.479.0", "mailgun.js": "^9.4.1", "motion": "^12.15.0", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "posthog-js": "^1.249.0", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.46"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^22.15.29", "@types/pg": "^8.15.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "file-type": "^20.5.0", "glob": "^10.4.5", "pg": "^8.16.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.8", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}}